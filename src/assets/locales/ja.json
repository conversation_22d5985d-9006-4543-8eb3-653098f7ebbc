{"common": {"cancel": "キャンセル", "OK": "OK", "clear": "クリア", "refresh": "リフレッシュ", "lang": "日本語", "doc": "ドキュメント", "connections": "接続", "inject-sdk": "SDKを注入", "submit": "送信", "search": "検索", "reset": "リセット", "device-id": "デバイスID", "os": "オペレーティング システム", "browser": "ブラウザ", "harmony": "HarmonyOS", "rn": "React Native", "miniprogram": "Mini Program", "mpwechat": "WeChat Mini Program", "mpqq": "QQ Mini Program", "mpalipay": "Alipay Mini Program", "mpdouyin": "Douyin Mini Program", "mptoutiao": "Toutiao Mini Program", "mpbaidu": "Baidu Mini Program", "mpfeishu": "Feishu Mini Program", "mpkuaishou": "Kuaishou Mini Program", "mpjd": "Jingdong Mini Program", "mptoutiaolt": "Toutiao Lite Mini Program", "mpdouyinlt": "Douyin Lite Mini Program", "mphuoshan": "<PERSON><PERSON><PERSON> Mini Program", "mpxigua": "Xigua Mini Program", "mpppx": "Pipixia Mini Program", "mpdingtalk": "Dingtalk Mini Program", "mpxhs": "Xiaohongshu Mini Program", "project": "プロジェクト", "debug": "デバッグ", "title": "タイトル", "start-debug": "デバッグを開始", "online-debug": "RTデバッグ", "offline-debug": "ログ再生", "createdAt": "作成日時", "updatedAt": "更新日時", "actions": "アクション", "delete": "削除", "confirm": "確認", "copied": "コピーされました", "prev": "前のページ", "next": "次のページ", "replay-lab": "リプレイラボ", "toc": "目次"}, "error": {"oops": "おっと！エラーが発生しました。", "actions": "<1>リフレッシュ</1>するか、<3><0>報告</0></3>してください。", "try-again": "リフレッシュ", "report": "報告", "no-frames": "有効なエラー情報が見つかりませんでした"}, "inject": {"web": {"title": "WEB", "load-sdk": "テストプロジェクトで <script> をロードする；", "init-sdk": "<0>次に、初期化し、</0> <1>構成オプション</1> を確認します：", "plugins": "<0>PageSpyは必要に応じてプラグインを統合し、SDKの機能を拡張できます。例えば:</0> <1>DOMの変更を記録</1>、<2>オフラインキャッシュ</2> <3>などの機能があります。必要に応じて、</3> <4>プラグインの詳細</4>を確認できます;"}, "mp": {"title": "Mini Program", "request-host": "page-spy サービスドメインを小プログラムのhttp、websocketリクエストのホワイトリストに入力します。開発環境以外では、小プログラムはhttpsとwssプロトコルの使用を強制しますので注意してください。", "install-sdk": "まず、プロジェクトに依存関係をインストールします。さまざまなプラットフォーム向けの SDK が提供されており、必要なものを選択できます。", "init-sdk": "<0>エントリーファイルでSDKをインポートし、インスタンス化します。</0> <1>構成オプション</1> を確認します："}, "harmony": {"title": "鸿蒙", "install-sdk": "対象のHAPディレクトリに依存関係をインストール:", "init-sdk": "<0>適切な場所でSDKをインポートし、初期化します。EntryAbilityを例として使用します。</0> <1>設定オプション</1>を表示:"}, "rn": {"title": "React Native", "install-sdk": "まず、プロジェクトに依存関係をインストールします:", "init-sdk": "<0>エントリーファイルでSDKをインポートし、インスタンス化します。</0> <1>構成オプション</1> を確認します：", "storage-plugin": "<0>@react-native-async-storage/async-storage をご利用の場合、独自のプラグインを提供しています：</0> <1>Async Storage plugin</1>"}, "end": "以上がすべてです。", "start-debug": "<1>接続</1>メニューからトップに移動し、デバッグを開始します！"}, "banner": {"title": "多機能<br />リモートデバッグツール", "desc": "Google Chromeのコンソールのように簡単にリモートデバッグを開始します。", "goStart": "デバッグを開始", "goGithub": "GitHub リポジトリ"}, "intro": {"does": "PageSpyでできること", "doesTitle": "ランタイムの検出、<br />リモート操作！", "provides": "提供するもの", "providesTitle": "使用準備の整った <1>SDK</1> および<br /> <4>クライアントデバッグ</4>", "load-script": "まず、ファイルを読み込む", "init-instance": "次に、設定（任意）して初期化する", "welcome": "PageSpyへようこそ"}, "selConn": {"title": "接続を選択", "label": "接続", "placeholder": "接続を選択"}, "devtool": {"device": "デバイス", "system": "システム", "browser": "ブラウザ", "platform": "プラットフォーム", "version": "バージョン", "eval-plugin-required": "小プログラム環境で動的にコードを実行する必要がある場合、クライアント側で SDK プラグイン @huolala-tech/page-spy-plugin-mp-eval を使用してください", "mp-warning": "レビューのためにミニ プログラムを送信するときは、必ずコード内の plugin-mp-eval を削除してください。削除しないと、レビューが失敗します。", "menu": {"Console": "コンソール", "Network": "ネットワーク", "System": "システム", "Page": "ページ", "Storage": "ストレージ", "MPPage": "Mini Program ページ", "MPSystem": "Mini Program システム"}}, "shortcuts": {"title": "ショートカット", "enter": "コードを実行", "tab": "タブを挿入（2スペース）", "shift+enter": "改行", "cmd+k": "パネルをクリア", "ctrl+l": "パネルをクリア", "updown": "入力履歴を切り替え"}, "console": {"run": "実行", "placeholder": "実行可能なコード", "newContent": "新しいメッセージ", "auto-scroll-on": "自動スクロールオン", "auto-scroll-off": "自動スクロールオフ", "mp-code-error": "コードの形式が間違っています。注意: 現在、ミニ プログラム環境は ES5 構文のみをサポートしています。", "error-trace": {"title": "エラーの詳細", "hints": "使用のヒント", "message-title": "エラーメッセージ", "stack-title": "エラースタック", "source-filename": "ソースファイル", "fetch-minify-fail": "ソースファイルの取得に失敗", "none-sourcemap": "ソースマップが見つかりません", "fetch-sourcemap-fail": "ソースマップの取得に失敗", "failed-title": "ソースコードの特定中に問題が発生しました", "failed-advice": "以下は修正と改善の提案です：", "fix-suggestion-1": "<0>1. ファイルの存在を確認：各エラースタックでソースコードを特定するためには、2つのファイル（圧縮ファイルとソースマップファイル）が要求されます。ソースマップファイルの取得規則は圧縮ファイル内の</0><1>//# sourceMappingURL=*</1><2>に指定されたアドレスに従います；</2>", "fix-suggestion-2": "2. ネットワークの問題を除外：ソースファイルが存在するサーバーにアクセス制限がある場合、リソースの取得に失敗する可能性があるため、ネットワークリクエストが正常かどうかをコンソールで確認できます。"}, "non-serializable": "<non-serializable>"}, "network": {"open-in-new-tab": "新しいタブで開く", "copy-link-address": "リンクアドレスをコピー", "copy-as-curl": "cURLとしてコピー"}, "system": {"overview": "概要", "feature": "機能", "unsupport": "サポートされていないリスト", "reference": "チェックルールを確認"}, "page": {"element": "要素", "device": "デバイス"}, "storage": {"empty-detail": "値を表示するアイテムを選択", "entries-be-modified": "一部のエントリが変更されました", "data-be-stale": "データが古くなっている可能性があります", "total-entries": "合計エントリー"}, "mp-page": {"page-stack": "ページスタック"}, "socket": {"client-name": "クライアント", "client-not-in-connection": "現在の接続でデバッグするクライアントが見つかりませんでした。クライアントのWebSocketを確認してください。", "client-not-found": "クライアントが見つかりません", "client-offline": "クライアントがオフラインです", "client-fail": "クライアントの接続が切断されました。関連するテスターにお問い合わせください。", "debug-name": "あなた", "debug-offline": "あなたはオフラインです", "reconnect-fail": "接続が切断されました", "reconnect-fail-desc": "再接続に失敗しました。ページをリフレッシュするか、再選択してください。", "room-not-found": "ルームが存在しません", "network-timeout": "ネットワークエラー、接続がタイムアウトしました", "server-down": "サーバーエラー", "room-secret": "ルームパスワード", "secret": "パスワード", "secret-placeholder": "6桁の部屋のパスワードを入力してください", "invalid-secret": "パスワードが無効です"}, "connections": {"select-os": "オペレーティング システムを選択", "select-browser": "ブラウザを選択", "select-mp": "Mini Program を選択", "maximum-alert": "一部の接続のみが表示されます。条件によってさらに絞り込んでください。", "status": {"active": "デバッグ中", "wait": "デバッグ待ち", "inactive": "デバッグ不可"}}, "replay": {"list-title": "再生リスト", "title": "ログ再生", "intro": "<0>何が</0> <1>ログリプレイ</1>ですか", "invalid-params": "リンク内で有効なURLパラメータが見つかりません。", "invalid-blob": "<0>URLからのパラメータの読み取りに失敗しました；オブジェクトは既にクリアされている可能性があります</0><1><0></0></1>", "related-time": "相対時間", "absolute-time": "絶対時間", "select-log": "ログを選択", "log-type": "ログの種類", "online-link": "オンラインリンク", "online-link-tips": "オンラインログのリンクを入力してください", "local-file": "ローカルファイル", "local-file-tip": "ローカルファイルを選択してください", "file-info": "ファイル情報", "file-status": "ファイルの状態", "file-size": "ファイルサイズ", "debug": "再生", "date": "日付", "others": "その他のログ", "delete-title": "ログを削除", "delete-desc": "このログを削除してもよろしいですか？", "client": "クライアント情報", "download-file": "ダウンロード", "update-layout": "レイアウトの更新", "speed": "倍速", "delete-select": "選択されたものを削除", "delete-select-desc": "選択したログを削除してもよろしいですか？", "unsupport-spread": "<0>デフォルトではオブジェクトを展開できません。<1><0>serializeData: true</0></1> を設定して有効にします。</0>", "remark": "備考", "meta-info": "基本情報", "meta": {"ua": "UserAgent", "title": "タイトル", "url": "リンク", "remark": "備考"}}, "lab": {"welcome": "リプレイラボへようこそ！", "statement": "ログデータはサーバーを経由せず、完全にローカルクライアント上で処理されるので、安心してお使いください。", "only-pc": "PC版で体験してください :)", "one-line": "たった1行で、", "load-pageSpy": "プロジェクトにPageSpyを導入できます。", "then": "導入後、", "feedback-demo": "ページの右下に「フィードバック」ウィジェットが表示されます。", "try-click": "クリックして試してみてください！", "after": "すべて準備が整ったら、", "click-upload": "下のボタンをクリックして、先ほどエクスポートしたファイルをアップロードしてください。", "upload-btn": "ファイルをアップロード", "congratulation": "開発者の皆さん、幸運を祈ります！", "desc": "（ここではログリプレイの機能のみを表示していますが、PageSpyはリアルタイムオンラインデバッグもサポートしています。詳細はドキュメントをご確認ください。）"}}