{"common": {"cancel": "취소", "OK": "확인", "clear": "지우기", "refresh": "새로 고침", "lang": "한국인", "doc": "문서", "connections": "연결", "inject-sdk": "SDK 삽입", "submit": "제출", "search": "검색", "reset": "초기화", "device-id": "장치 ID", "os": "운영 체제", "browser": "브라우저", "harmony": "HarmonyOS", "rn": "React Native", "miniprogram": "Mini Program", "mpwechat": "WeChat Mini Program", "mpqq": "QQ Mini Program", "mpalipay": "Alipay Mini Program", "mpdouyin": "Douyin Mini Program", "mptoutiao": "Toutiao Mini Program", "mpbaidu": "Baidu Mini Program", "mpfeishu": "Feishu Mini Program", "mpkuaishou": "Kuaishou Mini Program", "mpjd": "Jingdong Mini Program", "mptoutiaolt": "Toutiao Lite Mini Program", "mpdouyinlt": "Douyin Lite Mini Program", "mphuoshan": "<PERSON><PERSON><PERSON> Mini Program", "mpxigua": "Xigua Mini Program", "mpppx": "Pipixia Mini Program", "mpdingtalk": "Dingtalk Mini Program", "mpxhs": "Xiaohongshu Mini Program", "project": "프로젝트", "debug": "디버그", "title": "제목", "start-debug": "디버깅을 시작", "online-debug": "실시간 디버깅", "offline-debug": "로그 재생", "createdAt": "생성 시간", "updatedAt": "업데이트 시간", "actions": "조작", "delete": "삭제", "confirm": "확인", "copied": "복사되었습니다", "prev": "이전 페이지", "next": "다음 페이지", "replay-lab": "리플레이 연구소", "toc": "목차"}, "error": {"oops": "이런! 오류가 발생했습니다.", "actions": "<1>새로 고침</1>하거나 <3><0>보고</0></3>해 주세요.", "try-again": "새로 고침", "report": "보고", "no-frames": "유효한 오류 정보를 찾을 수 없습니다"}, "inject": {"web": {"title": "WEB", "load-sdk": "테스트 프로젝트에 <script>를 로드하십시오;", "init-sdk": "<0>그런 다음 초기화하고</0> <1>구성 옵션</1>을 확인하십시오:", "plugins": "<0>PageSpy는 필요에 따라 플러그인을 통합하여 SDK의 기능을 확장할 수 있습니다. 예를 들어:</0> <1>DOM 변경 기록</1>, <2>오프라인 캐싱</2> <3>등과 같은 기능이 있습니다. 필요한 경우, </3> <4>플러그인 세부 정보</4>를 확인할 수 있습니다;"}, "mp": {"title": "Mini Program", "request-host": "\"page-spy\" 서비스 도메인을 미니 프로그램의 HTTP 및 WebSocket 요청 화이트리스트에 추가하십시오. 개발 환경을 제외하고는 미니 프로그램에서는 HTTPS 및 WSS 프로토콜을 의무적으로 사용해야 합니다.", "install-sdk": "먼저 프로젝트에 종속성을 설치하십시오. 우리는 다양한 플랫폼에 대한 SDK를 제공하므로 필요한 것을 선택할 수 있습니다.", "init-sdk": "<0>엔트리 파일에서 SDK를 가져와 인스턴스화합니다.</0> <1>구성 옵션</1>을 확인하십시오:"}, "harmony": {"title": "홍몽", "install-sdk": "대상 HAP 디렉토리에 종속성 설치:", "init-sdk": "<0>적절한 위치에서 SDK를 가져오고 초기화합니다. EntryAbility를 예로 사용합니다.</0> <1>구성 옵션</1>을 확인:"}, "rn": {"title": "React Native", "install-sdk": "먼저 프로젝트에 종속성을 설치하십시오.", "init-sdk": "<0>엔트리 파일에서 SDK를 가져와 인스턴스화합니다.</0> <1>구성 옵션</1>을 확인하십시오:", "storage-plugin": "<0>@react-native-async-storage/async-storage를 사용하는 경우, 독립적인 플러그인을 제공합니다：</0> <1>Async Storage plugin</1>"}, "end": "모두 여기까지입니다.", "start-debug": "<1>연결</1> 메뉴에서 상단으로 이동하여 디버그를 시작하세요!"}, "banner": {"title": "다기능<br />원격 디버깅 도구", "desc": "Google Chrome 콘솔을 사용하는 것처럼 간단히 원격 디버깅을 시작하세요.", "goStart": "디버깅 시작", "goGithub": "GitHub 저장소"}, "intro": {"does": "PageSpy가 할 수 있는 일", "doesTitle": "런타임 감지, <br /> 원격 조작!", "provides": "제공하는 것", "providesTitle": "사용 준비가 된 <1>SDK</1><br />및 <4>클라이언트 디버그</4>", "load-script": "먼저, 파일을 로드합니다", "init-instance": "그런 다음, 설정(선택 사항)하고 초기화합니다", "welcome": "PageSpy에 오신 것을 환영합니다."}, "selConn": {"title": "연결 선택", "label": "연결", "placeholder": "연결 선택"}, "devtool": {"device": "장치", "system": "시스템", "browser": "브라우저", "platform": "플랫폼", "version": "버전", "eval-plugin-required": "미니 프로그램 환경에서 동적으로 코드를 실행해야 하는 경우 클라이언트에서 SDK 플러그인 @huolala-tech/page-spy-plugin-mp-eval 을 사용하십시오.", "mp-warning": "검토를 위해 미니 프로그램을 제출할 때 코드에서 plugin-mp-eval 를 삭제해야 합니다. 그렇지 않으면 검토가 실패합니다.", "menu": {"Console": "콘솔", "Network": "네트워크", "System": "시스템", "Page": "페이지", "Storage": "저장소", "MPPage": "Mini Program 페이지", "MPSystem": "Mini Program 시스템"}}, "shortcuts": {"title": "단축키", "enter": "코드 실행", "tab": "탭 삽입 (2 스페이스)", "shift+enter": "줄 바꿈", "cmd+k": "패널 지우기", "ctrl+l": "패널 지우기", "updown": "입력 기록 전환"}, "console": {"run": "실행", "placeholder": "실행 가능한 코드", "newContent": "새로운 메시지", "auto-scroll-on": "자동 스크롤링 켜기", "auto-scroll-off": "자동 스크롤링 끄기", "mp-code-error": "코드 형식이 잘못되었습니다. 참고 사항: 현재 미니 프로그램 환경은 ES5 구문만 지원합니다.", "error-trace": {"title": "오류 세부 정보", "hints": "사용 팁", "message-title": "오류 메시지", "stack-title": "오류 스택", "source-filename": "소스 파일", "fetch-minify-fail": "소스 파일 가져오기 실패", "none-sourcemap": "소스 맵을 찾을 수 없음", "fetch-sourcemap-fail": "소스 맵 가져오기 실패", "failed-title": "소스 코드 위치 찾기 중 문제가 발생했습니다", "failed-advice": "다음은 수정 및 개선 제안입니다:", "fix-suggestion-1": "<0>1. 파일이 존재하는지 확인하십시오. 각 오류 스택은 소스 코드를 확인하기 위해 2 개의 파일 (압축 파일 및 소스 맵 파일)을 요청합니다. 소스 맵 파일 가져오기 규칙은 압축 파일 내</0><1>//# sourceMappingURL=*</1><2>에 지정된 주소를 기준으로합니다.</2>", "fix-suggestion-2": "2. 네트워크 문제 제외: 원본 파일이있는 서버에 액세스 제한이있는 경우 리소스 가져오기 실패가 발생할 수 있으므로 네트워크 요청이 정상적인지 확인하려면 콘솔을 열 수 있습니다."}, "non-serializable": "<non-serializable>"}, "network": {"open-in-new-tab": "새 탭에서 열기", "copy-link-address": "링크 주소 복사", "copy-as-curl": "cURL로 복사"}, "system": {"overview": "개요", "feature": "기능", "unsupport": "지원하지 않는 목록", "reference": "검사 규칙 확인"}, "page": {"element": "요소", "device": "장치"}, "storage": {"empty-detail": "값을 볼 항목을 선택하십시오.", "entries-be-modified": "일부 항목이 수정되었습니다", "data-be-stale": "데이터가 오래되었을 수 있습니다", "total-entries": "총 항목"}, "mp-page": {"page-stack": "페이지 스택"}, "socket": {"client-name": "클라이언트", "client-not-in-connection": "현재 연결에서 디버그 대상 클라이언트를 찾을 수 없습니다. 클라이언트의 WebSocket을 확인하세요.", "client-not-found": "클라이언트를 찾을 수 없습니다", "client-offline": "클라이언트가 오프라인 상태입니다", "client-fail": "클라이언트의 연결이 끊어졌습니다. 관련 테스터에 문의하십시오.", "debug-name": "당신", "debug-offline": "당신은 오프라인 상태입니다", "reconnect-fail": "연결이 끊어졌습니다", "reconnect-fail-desc": "재연결에 실패했습니다. 페이지를 새로 고치거나 다시 선택하십시오.", "room-not-found": "방이 존재하지 않습니다", "network-timeout": "네트워크 오류, 연결 시간 초과", "server-down": "서버 오류", "room-secret": "방 비밀번호", "secret": "비밀번호", "secret-placeholder": "6 자리 숫자의 방 비밀번호를 입력하십시오", "invalid-secret": "잘못된 비밀번호"}, "connections": {"select-os": "운영 체제 선택", "select-browser": "브라우저 선택", "select-mp": "Mini Program 선택", "maximum-alert": "일부 연결만 표시됩니다. 조건을 통해 추가로 필터링하세요.", "status": {"active": "디버깅 중", "wait": "디버깅 대기 중", "inactive": "디버깅 불가"}}, "replay": {"list-title": "재생 목록", "title": "로그 재생", "intro": "<0>무엇인가요</0> <1>로그 재생</1>", "invalid-params": "링크에서 유효한 URL 매개변수를 찾을 수 없습니다.", "invalid-blob": "<0>URL에서 매개변수를 읽는 데 실패했습니다. 객체가 이미 지워졌을 수 있습니다</0><1><0></0></1>", "related-time": "상대 시간", "absolute-time": "절대 시간", "select-log": "로그 선택", "log-type": "로그 유형", "online-link": "온라인 링크", "online-link-tips": "온라인 로그 링크를 입력하세요", "local-file": "로컬 파일", "local-file-tip": "로컬 파일을 선택하세요", "file-info": "파일 정보", "file-status": "파일 상태", "file-size": "파일 크기", "debug": "재생", "date": "날짜", "others": "기타 로그", "delete-title": "로그 삭제", "delete-desc": "이 로그를 삭제하시겠습니까?", "client": "클라이언트 정보", "download-file": "다운로드", "update-layout": "레이아웃 업데이트", "speed": "속도", "delete-select": "선택 항목 삭제", "delete-select-desc": "선택한 로그를 삭제하시겠습니까?", "unsupport-spread": "<0>기본적으로 객체를 확장할 수 없습니다. <1><0>serializeData: true</0></1> 을 설정하여 활성화하십시오.</0>", "remark": "비고", "meta-info": "기본 정보", "meta": {"ua": "UserAgent", "title": "제목", "url": "링크", "remark": "비고"}}, "lab": {"welcome": "리플레이 실험실에 오신 것을 환영합니다!", "statement": "로그 데이터는 서버를 거치지 않고 완전히 로컬 클라이언트에서 처리되므로 안심하고 사용할 수 있습니다.", "only-pc": "PC 버전에서 체험해 보세요 :)", "one-line": "한 줄만으로,", "load-pageSpy": "프로젝트에 PageSpy를 통합할 수 있습니다.", "then": "통합 후,", "feedback-demo": "페이지 오른쪽 하단에 '피드백' 위젯이 표시됩니다.", "try-click": "한번 눌러보세요!", "after": "모든 준비가 완료되면,", "click-upload": "아래 버튼을 클릭하여 방금 내보낸 파일을 업로드하세요.", "upload-btn": "파일 업로드", "congratulation": "개발자 여러분, 행운을 빕니다!", "desc": "(여기서는 로그 리플레이 기능만 보여줍니다. PageSpy는 실시간 온라인 디버깅도 지원하니 자세한 내용은 문서를 참고하세요.)"}}