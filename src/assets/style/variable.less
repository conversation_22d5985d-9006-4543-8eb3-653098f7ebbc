@primary-color: #ffac4a;

// Text Color
@text-primary: #313233;
@text-regulary: #626466;
@text-secondary: #939699;
@text-placeholder: #c4c8cc;

// Box-Shadow
@light-box-shadow: 0 2px 10px 1px rgba(0, 0, 0, 0.05);
@dark-box-shadow: 0 2px 10px 1px rgba(0, 0, 0, 0.35);
@gradient: linear-gradient(45deg, #fff, #ffac4a 52%, #ffb864);

// Border
@light-border: #dedede;
@dark-border: #282828;

#compose {
  .gradient-text() {
    color: @primary-color;
    @supports (background-clip: text) {
      background: @gradient;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}

// App
@header-height: 64px;
@light-bg: rgba(255, 255, 255, 1);
@dark-bg: rgb(84, 84, 84, 1);
