.layouts {
  height: 100%;
  overflow: hidden;
  .ant-layout-header {
    position: relative;
    padding-inline: 0;
    z-index: 100;
  }
  .ant-layout-content {
    overflow: auto;
  }
  .header {
    position: relative;
    padding-inline: 48px;
    height: var(--header-height);
    box-shadow: @light-box-shadow;
    background-color: var(--light-background);
    z-index: 10;
    &.is-dark {
      box-shadow: @dark-box-shadow;
      background-color: var(--dark-background);
      .logo-name {
        color: #ddd;
        word-break: keep-all;
      }
    }
  }
  .logo {
    height: var(--header-height);
    display: flex;
    align-items: center;
    gap: 32px;
    > a {
      height: 100%;
      display: inline-flex;
      place-items: center;
    }
    &-icon {
      height: 46px;
    }
    &-name {
      margin-left: 12px;
      margin-bottom: 0;
      color: #000;
    }
    .page-spy-version {
      display: inline-block;
      transform: translate(2px, -10px);
      font-size: 12px;
    }
    .producthunt-brand {
      height: 36px;
      @media screen and (max-width: 420px) {
        display: none;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .layouts {
    .header {
      padding-inline: 24px;
    }
  }
}
