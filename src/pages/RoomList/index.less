.room-list {
  &-sider {
    height: 100%;
    display: grid;
    padding: 24px;
    grid-template-rows: auto 1fr auto;
    .maximum-alert {
      position: relative;
      font-size: 12px;
      text-align: right;
      color: fade(@primary-color, 90%);
      &::before {
        content: '*';
        color: red;
        font-size: 14px;
        font-weight: 500;
        margin-right: 2px;
      }
    }
    .statistics {
      &-item {
        text-align: center;
        p:nth-child(1) {
          font-size: 12px;
          color: #999;
          text-align: center;
          margin-bottom: 8px;
        }
        p:nth-child(2) {
          cursor: pointer;
          font-size: 20px;
          font-weight: 500;
        }
      }
    }
  }
  .connection-item {
    margin-bottom: 24px;
    padding: 12px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
    transition: all ease-out 0.3s;
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0px 8px 4px rgba(0, 0, 0, 0.1);
    }
    &__title {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      gap: 4px;
      padding-block: 8px;
      text-align: center;
    }
    .custom-title {
      padding-inline: 8px;
      font-size: 12px;
      line-height: 18px;
      color: #fff;
      background-color: fade(@primary-color, 50%);
      border-radius: 999px;
      max-width: 80%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    &__info {
      > div {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 6px;
        &:first-child {
          margin-bottom: 8px;
        }
      }
    }
    .conn-detail {
      &__title {
        font-size: 12px;
        color: #999;
        text-align: center;
      }
      &__value {
        max-width: 100px;
        height: 42px;
        display: flex;
        justify-content: center;
        align-items: center;
        p {
          font-weight: 500;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        // > * {
        //   min-width: 0;
        // }
        img {
          width: 24px;
        }
      }
    }
  }
}
