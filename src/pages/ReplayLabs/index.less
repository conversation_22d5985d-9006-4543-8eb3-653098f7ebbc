.replay-lab {
  padding: 40px 80px 400px;
  height: 100%;
  color: white;
  text-align: center;
  background: linear-gradient(-45deg, #ee7752, #e33d7d, #6d25e0, #23d5ab);
  background-size: 400% 400%;
  animation: gradient 30s ease infinite;
  text-shadow: 0 4px 4px rgba(0, 0, 0, 0.4);
  overflow: auto;
  h1 {
    font-size: 58px;
  }
  h2 {
    font-size: 42px;
  }
  h3 {
    font-size: 32px;
  }
  h4 {
    font-size: 20px;
  }
  @media screen and (max-width: 768px) {
    h1 {
      font-size: 46px;
    }
    .statement {
      font-size: 12px;
    }
  }
  .code-block {
    margin-block: 40px;
    code * {
      font-size: 16px;
    }
  }
  .statement {
    &:before {
      content: '*';
      margin-right: 4px;
    }
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
