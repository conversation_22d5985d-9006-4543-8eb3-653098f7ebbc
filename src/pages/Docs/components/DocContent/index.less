@paragraph-space: 12px;

.doc-content {
  display: flex;
  align-items: flex-start;
  gap: 40px;
  padding-inline: 24px;
  height: 100%;
  overflow: auto;
  transition: filter ease-in-out 100ms;
  &.loading {
    filter: blur(3px);
  }
  .paragraph {
    flex: 1;
    min-width: 0;
    word-break: break-word;
    .content {
      min-height: 100%;
      > * + * {
        margin-top: @paragraph-space;
      }
    }
    p,
    li,
    summary,
    .header-anchor {
      font-size: 16px;
      font-weight: 400;
      line-height: 1.65;
    }

    details[open] summary {
      margin-bottom: @paragraph-space;
    }
    /* 元素样式 */
    blockquote {
      padding: 8px 16px;
      border-left: 4px solid @primary-color;
      border-radius: 4px;
      background-color: #212121;
      margin-bottom: @paragraph-space;
      p {
        margin: 0;
      }
    }
    each(range(6), {
      h@{value} {
        margin-top: 28px !important;
        font-size: 42px - (@value - 1) * 6px;
        font-weight: 900 - 100 * @value;
        color: #f3f3f3;
      }
    })
      p {
      margin-bottom: @paragraph-space;
    }
    li {
      > p {
        margin-bottom: @paragraph-space;
        &:only-child {
          margin-bottom: 0;
        }
      }
    }
    b,
    strong {
      font-weight: 700;
      color: #f3f3f3;
    }
    a {
      color: #b392f0;
      font-family: Cascadia Code, Menlo, Courier, monospace;
      &:hover {
        text-decoration: underline;
      }
    }
    code {
      color: #d1d1d1;
      padding: 2px 6px;
      border-radius: 5px;
      background-color: #444;
      font-family: Cascadia Code, Menlo, Courier, monospace;
    }
    pre code {
      color: initial;
      padding: initial;
      border-radius: initial;
      background-color: initial;
    }
    ul,
    ol {
      padding-left: 20px;
      margin-bottom: 12px;
    }
    iframe {
      display: block;
      // width: 90%;
      margin: 0 auto;
      border-radius: 8px;
      border: none;
      aspect-ratio: 16 / 9;
      border: 2px solid #333;
    }
    span.property {
      background-color: #24292e;
      color: #b392f0;
      padding: 2px 6px;
      border-radius: 5px;
    }
    table {
      margin-top: 12px;
      border-collapse: collapse;
      border: 1px solid #333;
      border-radius: 4px;
    }
    th,
    td {
      border: 1px solid #333;
      border-radius: 4px;

      padding: 6px 14px;
    }
    img {
      display: block;
      width: 90%;
      margin: 0 auto @paragraph-space;
      border-radius: 8px;
    }
    .code-block {
      margin-bottom: @paragraph-space;
    }
  }

  .navigation {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    margin-top: 80px;
    background-color: var(--dark-background);
    > div {
      cursor: pointer;
      &:hover .footer-link__title {
        color: #b67cff;
      }
    }
    &-menus {
      display: flex;
      justify-content: center;
      align-items: center;
      transition: transform ease-in-out 150ms;
      &:active {
        transform: scale(1.25);
      }
    }
    .footer-link {
      text-decoration: none;
      display: flex;
      flex-direction: column;
      &__head {
        display: flex;
        align-items: center;
        color: @text-regulary;
        span {
          font-size: 12px;
        }
      }
      &__title {
        margin-top: 6px !important;
        color: #9e51ff;
        font-size: 16px;
        font-weight: 700;
        transition: color linear 0.3s;
      }
    }
    &-next .footer-link {
      align-items: end;
    }
  }

  .toc {
    flex-shrink: 0;
    flex-grow: 0;
    min-width: 0;

    display: none;
    position: sticky;
    top: 0;
    padding-block: 100px;
    font-size: 12px;
    line-height: 2;
    overflow: auto;
    .toc-navs__item {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    a {
      color: #808080;
      transition: color ease-in-out 150ms;

      &:hover {
        color: #ddd;
      }
      &.active {
        color: #fff;
        font-weight: 500;
      }
    }
  }
}

@media screen and (min-width: 768px) {
  .doc-content {
    .toc {
      display: block;
      flex-basis: 240px;
    }
  }
}

@media screen and (min-width: 1024px) {
  .doc-content {
    padding-left: var(--doc-sidebar-width);
    .paragraph {
      padding: 32px 0 64px 64px;
    }
  }
  .navigation-menus {
    visibility: hidden;
  }
}

@media screen and (max-width: 1024px) {
  .navigation {
    position: sticky;
    bottom: 0;
    width: 100vw;
    padding: 24px;
    transform: translate(-24px, 24px);
    border-top: 1px solid @dark-border;
  }
}

@media screen and (min-width: 1440px) {
  .doc-content {
    padding-left: calc(var(--doc-sidebar-width) + var(--doc-side-space) / 3);
    .toc {
      flex-basis: 320px;
    }
  }
}
