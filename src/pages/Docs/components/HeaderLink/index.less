.header-link {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  margin-left: -1rem;
  &:is(h1) {
    margin-bottom: 20px;
  }
  .header-anchor {
    // opacity: 0;
    // transition: opacity linear 0.15s;
    margin-top: 4px;
  }
  .matched-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.5);
    animation: fadeOut 1.5s ease-in-out 1 forwards;
    @keyframes fadeOut {
      to {
        opacity: 0;
      }
    }
  }
}
