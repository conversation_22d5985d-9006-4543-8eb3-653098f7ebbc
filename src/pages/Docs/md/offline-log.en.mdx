[release-1.7.3]: https://github.com/HuolalaTech/page-spy-web/releases/tag/v1.7.3
[public-data-event]: https://github.com/HuolalaTech/page-spy/blob/main/docs/plugin_zh.md#行为约定

import modalImg from '@/assets/image/screenshot/modal.png';
import replayImg from '@/assets/image/screenshot/replay-page.png';
import replayGif from '@/assets/image/screenshot/replay-page.gif';
import mpDataHarborImg from '@/assets/image/screenshot/mp-data-harbor.png';

**Log Replay**, as the name suggests, is about replaying previously generated logs. But why replay history logs? Where do these logs come from? How do we replay them? Let's explore each of these questions.

<img src={replayGif} />

## Why#why

Previously, PageSpy's online debugging solved many tricky problems, but there was a prerequisite for using PageSpy: **"The client and the debugger must be online simultaneously."** This prerequisite limited the use cases for PageSpy, for example:

- Two people (e.g. developer and tester) are required simultaneously to debug a problem.
- The connection is lost if the client moves to the background during debugging.

It also imposed constraints on PageSpy itself, such as:

- Consideration for the size of collected data and the network transmission load.

To address these issues and provide greater flexibility, PageSpy introduced the log replay feature in [version 1.7.3][release-1.7.3]!

## Where do the logs come from?#where

After PageSpy's SDK supported [plugin registration](./plugins), the development team quickly advanced the [DataHarborPlugin]({VITE_PLUGIN_DATA_HARBOR}) development.

> `Data Harbor`, the harbor of data.
>
> Concept: The data generated by PageSpy is continuously sent to the "data harbor". After organizing, packaging, and compressing the data, the "data port" stores the data in "containers" (memory or local temp file), awaiting further instructions.

It internally listens for the `"public-data"` event (see [what is the "public-data" event?](./plugins#convention)) to enable offline data caching. It also provides features to handle log data from the SDK-rendered component. When an issue is found on the client, testers can directly upload / download the data. This innovation breaks the previous requirement of having "the client and the debugger online simultaneously."

## How to Use#how-to-use

### Used in browser#browser

#### Step 1: Client-side SDK and plugin integration#step-1

```html
<html>
  <head>
    <!-- 1. Load PageSpy -->
    <script src="https://<your-pagespy-host>/page-spy/index.min.js"></script>

    <!-- 2. Load DataHarbor plugin to cache offline data and download or upload log data -->
    <script src="https://<your-pagespy-host>/plugin/data-harbor/index.min.js"></script>

    <!-- 3. Optionally, load the RRWeb plugin to record user interaction traces into offline logs -->
    <script src="https://<your-pagespy-host>/plugin/rrweb/index.min.js"></script>

    <script>
      // 4. Register plugins, see config details at: https://github.com/HuolalaTech/page-spy/blob/main/packages/page-spy-plugin-data-harbor
      PageSpy.registerPlugin(new DataHarborPlugin(config));
      PageSpy.registerPlugin(new RRWebPlugin());

      // 5. Instantiate PageSpy
      window.$pageSpy = new PageSpy({
        // To avoid establishing a real-time connection between the SDK and the debugger, enable offline mode
        // offline: true
      });
    </script>
  </head>
</html>
```

After successful integration, a modal with upload and download buttons should appear when clicking on the client-rendered component.

<img src={modalImg} />

#### Step 2: Replay the logs#step-2

Go to the debugger, click the top menu "Debugging - Log Replay" to enter the replay list page. Select the JSON data uploaded / downloaded in the previous step to start replaying!

<img src={replayImg} />

#### Using with Other Plugins#plugins

DataHarborPlugin mainly collects and handles data. Additionally, PageSpy provides:

- [RRWebPlugin]({VITE_PLUGIN_RRWEB}): Uses `rrweb` to record DOM mutations. In the "Log Replay" panel on the left side of the debugger, devepler can see the user interaction traces.


### Used in mini-program#mp

The ***Log Replay*** feature also works in Mini Program. Please follow the steps below:

#### Step 1: Install the DataHarbor plugin for miniprogram #mp-step-1

```bash
yarn add @huolala-tech/page-spy-plugin-mp-data-harbor
```

#### Step 2: Register the plugin:#mp-step-2
```ts
import PageSpy from '@huolala-tech/page-spy-wechat';
import DataHarborPlugin from '@huolala-tech/page-spy-plugin-mp-data-harbor';

// register the plugin, see config details at：https://github.com/HuolalaTech/page-spy/blob/main/packages/page-spy-plugin-mp-data-harbor
const $dataHarborPlugin = new DataHarborPlugin(config)
PageSpy.registerPlugin($dataHarborPlugin);

const $pageSpy = new PageSpy({
  // ...
})

```

#### Step 3: Upload Offline Logs#mp-step-3

There are two ways to upload offline logs:

1. Call the `upload()` method of the plugin instance directly:
```ts
$dataHarborPlugin.upload().then(() => {
  console.log('Upload successfully');
})
```

2. With the DataHarbor plugin registered, there will be a new action button for offline log uploading on PageSpy panel:

<img src={mpDataHarborImg} />

### Differences#diff

1. DOM recording is not supported in miniprogram env, thus there is no `RRWebPlugin` for miniprogram。

2. The offline log in miniprogram env supports uploading only, no downloading.

## FAQ

1. How to upload / download logs manually?

[Click to see](./data-harbor#onOfflineLog)。

2. Where is offline log data stored?

After `DataHarborPlugin` receives the data, it first places it in an array in memory. When the data volume in the array reaches a threshold, it writes the data to a temporary file. By default, this threshold is 10MB. You can also configure it yourself:

```ts
new DataHarborPlugin({
  maximum: 1 * 1024 * 1024, // Write to a temporary file when the data in memory reaches 1MB
})
```