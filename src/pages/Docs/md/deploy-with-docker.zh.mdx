import { EmbedVideo } from '../components/EmbedVideo';
import { DeployNext } from '../components/DeployNext';

### 安装#install

```bash
docker run -d --restart=always -v ./log:/app/log -v ./data:/app/data -p 6752:6752 --name="pageSpy" ghcr.io/huolalatech/page-spy-web:latest
```

执行完成后，打开浏览器访问 http://localhost:6752 即可访问服务。

### 视频学习#video

<EmbedVideo title="deployWithDocker" />{' '}

### 接下来#next

<DeployNext />
