import offlineLogImg from '@/assets/image/screenshot/v1.9.2-offline-log-size.png';

## v2.0.0

- 🆕 Revamped browser-side modal with support for custom theme colors and branding. Added a time-range log selector and a notes field. See details: https://github.com/HuolalaTech/page-spy/pull/113
- 🆕 Added a method for mini-programs to trigger a modal displaying PageSpy information, with support for uploading offline logs. See details: https://github.com/HuolalaTech/page-spy/pull/112
- 🐛 Fixed some issues.

## v1.9.6

- 🆕 DataHarborPlugin added a new prototype method. See details: https://github.com/HuolalaTech/page-spy/pull/110;
  - `$harbor.pause()`: Pause recording;
  - `$harbor.resume()`: Resume recording, corresponding to `pause()`;
  - `$harbor.reharbor()`: Clear the recorded data and remakes it.
- 🆕 Add new prompt for "Object cannot be expanded" on the replay page;
- 🐛 Fixed the display of `application/x-www-form-urlencoded` payload. See details: https://github.com/HuolalaTech/page-spy-web/issues/267;

## v1.9.5

- 🆕 Add DockerHub image: https://hub.docker.com/r/huolalatech/page-spy-web;
- 🆕 Support deploy with Baota and add deploy guide；

## v1.9.4

- 🐛 Fix some issues;

## v1.9.3

- 🆕 The Network panel now supports keyword filtering. View details: https://github.com/HuolalaTech/page-spy-web/pull/262 ;
- 🆕 The Console panel of the log replay page supports scroll following, optimize message notification. See details: https://github.com/HuolalaTech/page-spy-web/pull/264 ;
- 🐛 SDK optimizes error messages for `ErrorPlugin`;

## v1.9.2

- The init parameters now include a `dataProcessor` option for filtering or processing data. See details: https://github.com/HuolalaTech/page-spy/pull/106;

  > <details>
  >  <summary>Click to expand and view examples.</summary>
  > 
  >   ```ts
  >   window.$pageSpy = new PageSpy({
  >     ...,
  >     dataProcessor: {
  >       console: (data) => {
  >         // log will be ignored if the content includes 'secret'
  >         if (data.logs.some(i => typeof i === 'string' && i.includes('secret'))) return false;
  >       },
  >       network: (data) => {
  >         // ignore metric requests
  >         if (/(sentry|metric|collect)/.test(data.url)) return false
  >       },,
  >       storage: (data) => {
  >         // change the cookie to "******" if the name starts with _
  >         if (data.type === "cookie" && data.action === "get") {
  >           data.data.forEach((i) => {
  >             if (i.name.startsWith("_")) {
  >               i.value = "*******";
  >             }
  >           });
  >         }
  >       },
  >     },
  >   });
  >   ```
  > </details>

- 🆕 Adjusted the uploaded log data, see details: https://github.com/HuolalaTech/page-spy/pull/107;

  <img src={offlineLogImg} />

- 🆕 Interaction adjustments:
  - Clicking on the log replay progress bar now maintains the playback state. See details: https://github.com/HuolalaTech/page-spy-web/pull/258;
  - The table header supports dragging, see details: https://github.com/HuolalaTech/page-spy-web/pull/257;
  - Added support for recognizing the Huawei browser, see details: https://github.com/HuolalaTech/page-spy-web/pull/256;

## v1.9.1

- 🆕 Added the `serializeData: boolean` option to the instantiation parameters, which specifies whether the SDK is allowed to serialize non-primitive data types when collecting offline data. Default is `false`. For more details, see: https://github.com/HuolalaTech/page-spy/pull/103.

  When enabled, data printed in the console can be expanded and viewed in the replay panel. Usage:

  ```ts
  window.$pageSpy = new PageSpy({
    ...
    // Note that serialization may have side effects, so it is disabled by default for safety;
    serializeData: true
  })
  ```

- 🆕 Optimized information display in the Network panel. For more details, see: https://github.com/HuolalaTech/page-spy-web/pull/239;
- 🆕 The replay panel now supports canvas content. For more details, see: https://github.com/HuolalaTech/page-spy-web/pull/238;
- 🆕 The left-side player during playback now shows "click" actions. The progress bar now only shows clicks and errors, with other data activities notified through the Tab. For more details, see: https://github.com/HuolalaTech/page-spy-web/pull/247;
- 🐛 Fixed the interaction of the replay log list. For more details, see: https://github.com/HuolalaTech/page-spy-web/pull/242;
- 🐛 Fixed several issues:
  - Server-side cross-origin configuration not taking effect;
  - Slow log deletion speed on the server side;
  - Offline logs now record the full lifecycle of network requests;
  - Formatted file names for SDK downloads;

## v1.9.0

- 🆕 All brand new document page;
- 🆕 The ability to dynamically execute code has been extracted from the mini-program SDK and encapsulated into an independent plugin, [@huolala-tech/page-spy-plugin-mp-eval](https://www.npmjs.com/package/@huolala-tech/page-spy-plugin-mp-eval), in order to minimize the impact on the review process for mini-programs;
- 🆕 Support miniprogram feature that transfer request payload to query string in GET request: https://github.com/HuolalaTech/page-spy-web/issues/199;
- 🐛 Fix compatibility issue of `getAccountInfoSync` in miniprogram SDK: https://github.com/HuolalaTech/page-spy-web/issues/213;
- 🐛 Fix type declaration of `updateRoomInfo` in miniprogram SDK: https://github.com/HuolalaTech/page-spy-web/issues/213;

### v1.8.10

This update primarily focuses on optimizing the "Error Source Code Location" feature. Specific changes include:

- 🆕 Enhanced error source code location support in the "Log Replay" page;
- 🆕 Added support for locating source code in console.xxx(Error) logs, in addition to captured errors. Details can be viewed in https://github.com/HuolalaTech/page-spy-web/pull/212;
- 🆕 Provided an option to configure tab characters (`\t`) in source code, allowing users to set 1 tab = N spaces;
- 🐛 Fixed interaction issues with the logo. Details in https://github.com/HuolalaTech/page-spy-web/issues/211;

## v1.8.9

- 🆕 Support for debugging React Native applications, related https://github.com/HuolalaTech/page-spy-web/pull/190;
- 🆕 Support for debugging HarmonyOS applications, related https://github.com/HuolalaTech/page-spy-web/pull/191 / https://github.com/HuolalaTech/page-spy/pull/88;
- 🆕 Web applications now support viewing `EventSource` request and response data, with an optimized network panel layout, related https://github.com/HuolalaTech/page-spy-web/pull/206 / https://github.com/HuolalaTech/page-spy-web/issues/202 / https://github.com/HuolalaTech/page-spy-web/issues/183;
- 🆕 Improved message types for faster debugging interface response, related https://github.com/HuolalaTech/page-spy-web/pull/201;
- 🆕 Room list page now limits the display to a maximum of 30 panels, related https://github.com/HuolalaTech/page-spy-web/pull/204;
- 🐛 Fixed several issues, related https://github.com/HuolalaTech/page-spy-web/issues/205 / https://github.com/HuolalaTech/page-spy/pull/95;

## v1.8.8

- 🆕 Refactor the "/replay" page, making interactions smoother when playing back long and large offline log data, see https://github.com/HuolalaTech/page-spy-web/pull/186;
- 🆕 Adapted for "UniApp packaged as a native App", see https://github.com/HuolalaTech/page-spy-web/pull/197;
- 🐛 Fixed an issue where a circual-reference in request headers caused the `Network` plugin to malfunction, see https://github.com/HuolalaTech/page-spy-web/pull/193;
- 🐛 Fixed the display of HTML tags on the `Page` panel, see https://github.com/HuolalaTech/page-spy-web/pull/196;

## v1.8.7
- 🆕 The SDK doesn't send message until the developer enter room, see https://github.com/HuolalaTech/page-spy/pull/84;
- 🐛 Fix the issue where reconnection after connection timeout;

## v1.8.6

- 🆕 After disconnection, the SDK will exponentially retry establishing connection, with a maximum interval not exceeding (1.5 ** 4 * 2000)ms, see https://github.com/HuolalaTech/page-spy/pull/78;
- 🆕 The SDK instantiation adds a new parameter `useSecret`, with a default value of `false`. When set to `true`, the SDK will generate a 6-digit random password for the room. The debugging terminal needs to know the client password before entering the debugging room, see https://github.com/HuolalaTech/page-spy/pull/78;
- 🆕 Support for dynamically updating the `title / project` parameters by using `window.$pageSpy.updateRoomInfo({ title: 'xxx', project: 'xxx' })`, see https://github.com/HuolalaTech/page-spy/pull/78;
- 🆕 After dragging the floating ball rendered by the browser SDK to the top, bottom, left, or right side, it will automatically retract, see https://github.com/HuolalaTech/page-spy/pull/80;
- 🆕 Support for get uploaded url, see https://github.com/HuolalaTech/page-spy/pull/81:

  ```js
  window.$harbor = new DataHarborPlugin()
  PageSpy.registerPlugin(window.$harbor)

  async function uploadLogManually() {
    const debugUrl = await window.$harbor.onOfflineLog('upload')
    console.log({ debugUrl })
  }
  ```

- 🆕 The `page-spy-api` no longer serializes data, resulting in a significant performance improvement;
- 🐛 fix some problems, see: https://github.com/HuolalaTech/page-spy-web/issues/172, https://github.com/HuolalaTech/page-spy/pull/79;

## v1.8.5

- 🆕 remove the unsupported arch binary;

## v1.8.4

- 🆕 Optimize the message cache , see https://github.com/HuolalaTech/page-spy-web/issues/169

## v1.8.3

- 🆕 Support debug Taro miniprogram, see [@huolala-tech/page-spy-taro](https://github.com/HuolalaTech/page-spy/tree/main/packages/page-spy-taro);
- 🆕 Support debug HarmonyOS App, see [@huolala/page-spy-harmony](https://ohpm.openharmony.cn/#/cn/detail/@huolala%2Fpage-spy-harmony);

  > Developed based on OpenHarmony API 9.

- 🐛 Fix response loss when an error occurs in NetworkPlugin

## v1.8.2

- 🐛 Fixed the issue where the npm package from the 1.8.0 and 1.8.1 version couldn't be installed on certain system architectures;

## v1.8.1

- 🆕 Added pre-submission reminders to the mini-program integration documentation;
- 🆕 Log replay page: Progress bar displays aggregated activity events.

## v1.8.0

- 🆕 `DataHarborPlugin` now supports uploading offline logs, and the debugging end has added a `https://<your-pagespy-host>/#/log-list` page;
- 🆕 Enhanced functionality on the offline log replay page, supporting layout adjustment via drag-and-drop and playback speed adjustment;
- 🆕 The mini-program now supports sending code for remote execution on the client side;

## More...

  For earlier release details, please visit the [Release](https://github.com/HuolalaTech/page-spy-web/releases) page.
