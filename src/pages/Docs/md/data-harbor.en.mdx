
# DataHarborPlugin API#api

## constructor()#constructor

- Type

  ```ts
  interface DataHarborConfig {
    // Specify the maximum bytes of offline log in memory.
    // Default 10MB.
    maximum?: number; // Only supported in browser

    // Specify which types of data to cache
    caredData?: Record<DataType, boolean>;

    // Specify the offline log filename, with the default being named according to the current time
    filename?: () => string;

    // Custom the behavior of download
    onDownload?: (data: CacheMessageItem[]) => void; // Only supported in browser
  }

  declare class DataHarborPlugin implements PageSpyPlugin {
    constructor(config?: DataHarborConfig);
  }
  ```


## Operate log manually#onOfflineLog

Manually download / upload offline logs.

- Type

  ```ts
  interface WholeLogActionParams {
    clearCache?: boolean;
    remark?: string;
  }

  interface PeriodLogActionParams {
    startTime: number;
    endTime: number;
    remark?: string;
  }

  declare class DataHarborPlugin {
    // Full logs
    upload(params?: WholeLogActionParams): Promise<string>;
    download(params?: WholeLogActionParams): Promise<void>;

    // time-period of logs
    uploadPeriods(params: PeriodLogActionParams): Promise<string>;
    downloadPeriods(params: PeriodLogActionParams): Promise<void>;
  }
  ```

  If the automatically rendered UI controls are hidden, or if you wish to automatically trigger operations on offline logs at certain times, this method can be used to achieve that.

  When operating on full logs, each time the user operates the log upload/download through the buttons on the modal, it is the complete log from the beginning to the end of the current session. However, when called manually, the recorded log data will be cleared by default and recording will start anew. You can control this by setting `clearCache: false` in the second parameter.

  Operating on time-period logs will not clear any data.

  After the upload is completed, the replay URL will be returned and printed to the console.
  
- Example

  ```ts
  window.$harbor = new DataHarborPlugin();

  // Upload full logs and clear data and re-record
  const url = await window.$harbor.upload();

  // Upload the logs of the most recent three minutes.
  const now = Date.now();
  const url = await window.$harbor.uploadPeriods({
    startTime: now - 3 * 60000,
    endTime: now,
    remark: 'Add a remark to the problems that occur in this section of the log.'
  });
  ```


## pause()#pause

Pause recording.

- Type

  ```ts
  declare class DataHarborPlugin {
    pause(): void;
  }
  ```

  More flexible control of logging behavior.

  The data generated by the program after the pause will not be recorded. Call `$harbor.resume()` to resume.
  
- Example

  ```ts
  window.$harbor = new DataHarborPlugin();
  
  // pause
  window.$harbor.pause();
  
  // resume
  window.$harbor.resume();
  ```


## resume()#resume


Resume records.

- Type

  ```ts
  declare class DataHarborPlugin {
    resume(): void;
  }
  ```
  
  More flexible control of logging behavior.

- Details

  Data during &lt;Pause - Resume&gt; will not be recorded.
  
- Example

  ```ts
  window.$harbor = new DataHarborPlugin();
  
  // pause
  window.$harbor.pause();
  
  // resume
  window.$harbor.resume();
  ```

## reharbor()#reharbor

Clear the recorded data and continue recording. In short, remastered.

- Type

  ```ts
  declare class DataHarborPlugin {
    reharbor(): void;
  }
  ```

- Example

  ```ts
  window.$harbor = new DataHarborPlugin();

  window.$harbor.reharbor();
  ```