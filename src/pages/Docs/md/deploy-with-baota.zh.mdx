import BaotaPageSpy from '@/assets/image/screenshot/baota-pagespy.png';
import BaotaInstall from '@/assets/image/screenshot/baota-install.png';
import { DeployNext } from '../components/DeployNext';

PageSpy 支持在宝塔面板的 Docker 应用商店一键部署。

### 前提#prerequire

安装宝塔面板，前往 [宝塔面板](https://www.bt.cn/new/index.html) 官网，选择对应的脚本下载安装。

### 安装#install

1. 前往「Docker - 应用商店」中找到 PageSpy，点击安装。

<img src={BaotaPageSpy} />

2. 设置基本信息，点击 "确定"。

<img src={BaotaInstall} />

3. 面板会自动进行应用初始化，大概需要 1 分钟左右，初始化完成后即可通过 6752 端口访问。

### 接下来#next

<DeployNext />
