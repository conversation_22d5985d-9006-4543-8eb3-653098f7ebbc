import BaotaPageSpy from '@/assets/image/screenshot/baota-pagespy.png';
import BaotaInstall from '@/assets/image/screenshot/baota-install.png';
import { DeployNext } from '../components/DeployNext';

PageSpy supports one-click deployment in the Baota Panel's Docker appstore.

### Prerequisites#prerequire

Install the Baota Panel by visiting the [Baota Panel](https://www.bt.cn/new/index.html) official website and selecting the appropriate script for installation.

### Installation#install

1. Go to "Docker - App Store," find PageSpy, and click install.

   <img src={BaotaPageSpy} />

2. Set the basic information and click "Confirm."

   <img src={BaotaInstall} />

3. The panel will automatically initialize the app, which takes about 1 minutes. After initialization, the service will be accessible via port 6752.

### Next#next

<DeployNext />
