import React from 'react';
import bannerImg from '@/assets/image/doc-banner.png';

import consoleImg from '@/assets/image/screenshot/console-panel.png';
import networkImg from '@/assets/image/screenshot/network-panel.png';
import pageImg from '@/assets/image/screenshot/page-panel.png';
import storageImg from '@/assets/image/screenshot/storage-panel.png';
import systemImg from '@/assets/image/screenshot/system-panel.png';
import whyIsPageSpyImg from '@/assets/image/screenshot/why-is-pagespy-zh.png';

export const githubUrl = import.meta.env.VITE_GITHUB_REPO;

<img src={bannerImg} style={{ width: '100%' }} />

### 什么是 PageSpy？#what-is-page-spy

**PageSpy** 是一款兼容 [Web](./browser) / [小程序](./miniprogram) / [React Native](./react-native) / [鸿蒙 App](./harmony) 等平台项目的开源调试平台。基于对原生 API 的封装，它将调用原生方法时的参数进行过滤、转化，整理成一定格式的消息供调试端消费；调试端收到消息数据后，提供类似本地控制台的功能界面将数据呈现出来。

### 为什么是 PageSpy?#why-is-page-spy

> 一图胜千言。

<img src={whyIsPageSpyImg} style={{ width: '100%' }} />

### 何时使用？#when-to-use

*任何无法在本地使用控制台调试的场景，都是 **PageSpy** 可以大显身手的时候！*
一起来看下面的几个场景案例：

- **本地调试 H5、Webview 应用**：以往有些产品提供了可以在 H5 上查看信息的面板，但移动端屏幕太小操作不便、显示不友好，以及信息被截断等问题；
- **远程办公、跨地区协同**：传统沟通方式如邮件、电话、视频会议等，沟通效率不高、故障信息不全面，容易误解误判；
- **用户终端上出现白屏问题**：传统定位问题的方式包括数据监控、日志分析等，这些方式依赖排障人员要理解业务需求场景、技术实现；

PageSpy 的目标，就是为包括以上场景的人员提供帮助。

### 界面概览#feature-overview

- Console 面板: 显示 `console.<log | info | warn | error | debug>` 输出的日志信息，可以发送代码到客户端执行；

<img src={consoleImg} />

- Network 面板: 显示客户端发出的网络请求信息；

<img src={networkImg} />

- Element 面板: 显示客户端当前界面，查看 HTML 节点树；

<img src={pageImg} />

- Storage 面板: 查看客户端本地的缓存数据；

<img src={storageImg} />

- Systems 面板: 显示客户端的系统信息，查看兼容性。

<img src={systemImg} />

除此之外，当有新的数据或者数据发生变化的时候会实时的收到通知。