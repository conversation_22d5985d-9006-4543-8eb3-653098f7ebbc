.console-panel {
  display: flex;
  flex-direction: column;
  &__content {
    position: relative;
    flex: 1;
    height: 0;
    display: flex;
    flex-direction: column;
    position: relative;
    margin-top: 8px;
    background-color: #fff;
    border-radius: 4px;
  }
  .console-list {
    flex: 1;
    height: 0;
    overflow: auto;
    padding-top: 8px;
    &__new {
      position: absolute;
      bottom: 100px;
      right: 100px;
    }
  }
  .page-spy-input {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    font-size: 12px;
    border-bottom: 1px solid #dedede;
    background-color: #fff;
    // box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
    .icon {
      font-size: 12px;
      font-weight: 700;
      color: #3d6fe9;
    }
  }
}
.mp-eval-plugin-info {
  background-color: #e6f4ff;
  padding: 12px;
  line-height: 1.5;
  display: flex;
  .anticon {
    margin-right: 10px;
    font-size: 20px;
    color: #69b1ff;
  }
}
