@controllerBtnSize: 14px;
.element-panel {
  .element-item {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    &.plain-text {
      margin-left: @controllerBtnSize;
      display: block;
      white-space: pre-wrap;
    }
    &.comment {
      transform: translateX(14px);
      color: #ababab;
    }
  }
  .element-controller {
    width: @controllerBtnSize;
    height: @controllerBtnSize;
  }
  .element-content {
    font-size: 12px;
    line-height: 1.4;
    word-break: break-all;
    code {
      font-size: 12px;
    }
    &__header,
    &__footer {
      color: #9b8a9b;
    }
    .tag-name {
      color: #731f72;
    }
  }
  .element-attrs {
    .attrs-item {
      color: #9b8a9b;
      &__name {
        color: #864114;
      }
      &__value {
        color: #181997;
      }
    }
  }
}
