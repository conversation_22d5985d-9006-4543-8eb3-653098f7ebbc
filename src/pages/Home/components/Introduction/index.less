.introduction {
  // padding-inline: 12vw;
  background-color: #fff;
  .intro-block {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 80px;
    padding-block: 80px;
    &.block-3 {
      background-color: rgba(@primary-color, 0.06);
      align-items: center;
    }
  }
  .small-title {
    margin-top: 100px;
    color: @primary-color;
    font-size: 16px;
    font-weight: 600;
  }
  .big-title {
    font-size: 42px;
    font-weight: 800;
  }
}

@media screen and (max-width: 768px) {
  .introduction {
    .intro-block {
      padding-inline: 12px;
      flex-direction: column;
      gap: 20px;
    }
    .block-1 {
      align-items: flex-end;
    }
    .block-2 {
      align-items: flex-start;
    }
    .block-1 .intro-block__title {
      order: -1;
      text-align: right;
    }
    .small-title {
      margin-top: 0;
    }
  }
}
