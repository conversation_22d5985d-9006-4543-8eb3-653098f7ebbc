.toggle-key {
  position: relative;
  color: @primary-color;
  transition: all ease-out 0.3s;
  cursor: pointer;
  &:hover {
    opacity: 0.7;
  }
  &:not(.active) {
    color: #999;
  }
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -2px;
    width: 20px;
    height: 4px;
    background-color: #999;
    border-radius: 99px;
    transition: all ease-out 0.3s;
  }
  &.active::after {
    width: 100%;
    background-color: @primary-color;
  }
}

.sdk-panel {
  .sdk-step {
    margin-bottom: 20px;
    &__title {
      color: #666;
      font-size: 16px;
      line-height: 32px;
      font-weight: 600;
    }
  }
}

.fade-enter-active,
.fade-exit-active {
  transition: all ease-out 300ms;
}
.fade-enter {
  opacity: 0;
  transform: translate3d(0, 100px, 0);
}
.fade-enter-active {
  opacity: 1;
  transform: translate3d(0px, 0, 0);
}
.fade-exit {
  opacity: 1;
  transform: translate3d(0px, 0, 0);
}
.fade-exit-active {
  opacity: 0;
  transform: translate3d(0, -50px, 0);
}
